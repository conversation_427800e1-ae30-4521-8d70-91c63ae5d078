/* styles/trainer-battle.css */
/* Trainer Battle Screen Styles */

#trainer-battle-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--main-blue) 0%, var(--pinky-red) 100%);
    z-index: 1000;
    display: none;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.trainer-battle-container {
    max-width: 600px;
    margin: 0 auto;
    background: var(--standard-background-color);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.trainer-battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.trainer-battle-title {
    margin: 0;
    color: var(--standard-text-color);
}

.trainer-battle-close {
    background: var(--pinky-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
}

.trainer-battle-close:hover {
    background: #d63031;
    transform: scale(1.1);
}

.battle-status {
    text-align: center;
    margin-bottom: 20px;
    color: var(--standard-text-color);
    font-weight: bold;
}

.battle-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.battle-side {
    text-align: center;
    flex: 1;
}

.battle-side h3 {
    margin-top: 0;
    color: var(--standard-text-color);
}

.vs-divider {
    font-size: 24px;
    margin: 0 20px;
    color: var(--really-grey);
    font-weight: bold;
}

.pokemon-display {
    border: 2px solid var(--light-grey);
    border-radius: 10px;
    padding: 10px;
    background: white;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.pokemon-display img {
    width: 64px;
    height: 64px;
}

.pokemon-display p {
    margin: 5px 0;
    color: var(--standard-text-color);
}

.pokemon-name {
    font-weight: bold;
}

.pokemon-level {
    color: var(--really-grey);
}

.pokemon-remaining {
    font-size: 12px;
    color: var(--really-grey);
}

.pokemon-display.no-pokemon {
    background: var(--light-grey);
    color: var(--really-grey);
}

.battle-log {
    background: var(--light-grey);
    border-radius: 10px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.battle-log h4 {
    margin-top: 0;
    color: var(--standard-text-color);
}

.battle-log-entry {
    margin-bottom: 5px;
    padding: 5px;
    background: white;
    border-radius: 5px;
    font-size: 14px;
    color: var(--standard-text-color);
}

.battle-result {
    display: none;
    text-align: center;
    margin-top: 20px;
}

.result-title {
    margin: 0 0 10px 0;
}

.result-title.victory {
    color: var(--green);
}

.result-title.defeat {
    color: var(--pinky-red);
}

.result-title.draw {
    color: var(--orange);
}

.result-description {
    color: var(--standard-text-color);
    margin-bottom: 15px;
}

.battle-finish-btn {
    background: var(--green);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.battle-finish-btn:hover {
    background: #00b894;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.battle-finish-btn:active {
    transform: translateY(0);
}

.pokemon-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    font-size: 12px;
    font-weight: bold;
    margin: 2px;
}

/* Trainer marker styles */
.trainer-marker {
    border: 2px solid var(--orange);
    border-radius: 50%;
    background: white;
}

.trainer-popup {
    min-width: 200px;
}

.trainer-popup .team-list {
    max-height: 150px;
    overflow-y: auto;
    margin: 5px 0;
}

.challenge-button {
    background: var(--main-blue);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.challenge-button:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.challenge-button:disabled {
    background: var(--really-grey);
    cursor: not-allowed;
    transform: none;
}

.team-requirement-warning {
    color: var(--pinky-red);
    font-size: 12px;
    margin-top: 5px;
    font-style: italic;
}
